import java.util.HashSet;
import java.util.Set;

public class HashSetTest {
    static class Person {
        String name;

        Person(String name) {
            this.name = name;
        }

        @Override
        public int hashCode() {
            int h = name.hashCode();
            System.out.println("hashCode() called for: " + name + " -> " + h);
            return h;
        }

        @Override
        public boolean equals(Object obj) {
            System.out.println("equals() called: comparing " + this.name + " with " + ((Person) obj).name);
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            return name.equals(((Person) obj).name);
        }

        @Override
        public String toString() {
            return name + "@" + Integer.toHexString(System.identityHashCode(this));
        }
    }

    public static void main(String[] args) {
        Set<Person> set = new HashSet<>();

        Person p1 = new Person("Alice");
        Person p2 = new Person("Alice"); // 不同对象，但内容相同

        System.out.println("=== Add p1 ===");
        System.out.println("Added: " + set.add(p1)); // 应该添加成功
        System.out.println("Set contains: " + set);

        System.out.println("\n=== Add p2 ===");
        System.out.println("Added: " + set.add(p2)); // 应该 equals() 被调用，添加失败
        System.out.println("Set contains: " + set);
    }
}
