import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class MultiConditionDemo {

    private final Lock lock = new ReentrantLock();
    private final Condition groupA = lock.newCondition();
    private final Condition groupB = lock.newCondition();

    public void awaitGroupA(String name) throws InterruptedException {
        lock.lock();
        try {
            System.out.println(name + " waiting on Group A...");
            groupA.await();  // 等待 A 条件
            System.out.println(name + " resumed from Group A!");
        } finally {
            lock.unlock();
        }
    }

    public void awaitGroupB(String name) throws InterruptedException {
        lock.lock();
        try {
            System.out.println(name + " waiting on Group B...");
            groupB.await();  // 等待 B 条件
            System.out.println(name + " resumed from Group B!");
        } finally {
            lock.unlock();
        }
    }

    public void signalGroupA() {
        lock.lock();
        try {
            System.out.println("Signaling Group A...");
            groupA.signalAll();  // 唤醒所有等待 groupA 的线程
        } finally {
            lock.unlock();
        }
    }

    public void signalGroupB() {
        lock.lock();
        try {
            System.out.println("Signaling Group B...");
            groupB.signalAll();  // 唤醒所有等待 groupB 的线程
        } finally {
            lock.unlock();
        }
    }

    public static void main(String[] args) throws InterruptedException {
        MultiConditionDemo demo = new MultiConditionDemo();

        // 使用线程工具创建 Group A 的线程
        Thread[] groupAThreads = ThreadHelper.createMultiple(new Runnable[]{
            ThreadHelper.safeTask(() -> demo.awaitGroupA("A1")),
            ThreadHelper.safeTask(() -> demo.awaitGroupA("A2"))
        }, "GroupA-Thread-");

        // 使用线程工具创建 Group B 的线程
        Thread[] groupBThreads = ThreadHelper.createMultiple(new Runnable[]{
            ThreadHelper.safeTask(() -> demo.awaitGroupB("B1")),
            ThreadHelper.safeTask(() -> demo.awaitGroupB("B2"))
        }, "GroupB-Thread-");

        Thread.sleep(2000); // 给线程时间进入等待状态

        demo.signalGroupA(); // 只唤醒 group A
        Thread.sleep(1000);

        demo.signalGroupB(); // 只唤醒 group B

        // 等待所有线程完成
        ThreadHelper.waitForAll(groupAThreads);
        ThreadHelper.waitForAll(groupBThreads);
    }
}
