import java.util.HashMap;


class Solution {

    HashMap<Integer, Integer> map = new HashMap<>();

    public TreeNode buildTree(int[] preorder, int[] inorder) {


        int n = preorder.length;
        // 记录中序遍历的索引
        for (int i = 0; i < inorder.length; i++) {
            map.put(inorder[i], i);
        }
        String sb = "adsad";

        return buildTree(preorder, inorder, 0, n - 1, 0, n - 1);

    }

    private TreeNode buildTree(int[] preorder, int[] inorder, int p_l, int p_r, int i_l, int i_r) {

        // 终止条件
        if (p_l > p_r) {
            return null;
        }

        // 获取先序根节点
        TreeNode root = new TreeNode(preorder[p_l]);
        // 根节点在中序遍历中的索引
        int index = map.get(root.val);
        // 计算左子树节点的个数
        int left_size = index - i_l;

        // 遍历左子树
        root.left = buildTree(preorder, inorder, p_l + 1, p_l + left_size, i_l, index - 1);
        // 遍历右子树
        root.right = buildTree(preorder, inorder, p_l + left_size + 1, p_r, index + 1, i_r);
        return root;
    }


    class TreeNode {
        int val;
        TreeNode left;
        TreeNode right;
        TreeNode() {}
        TreeNode(int val) { this.val = val; }
        TreeNode(int val, TreeNode left, TreeNode right) {
            this.val = val;
            this.left = left;
            this.right = right;
        }
    }
}
