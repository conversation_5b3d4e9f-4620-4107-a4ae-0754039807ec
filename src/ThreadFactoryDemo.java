import java.util.concurrent.CompletableFuture;

/**
 * 线程工厂使用示例
 */
public class ThreadFactoryDemo {
    
    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== 线程工厂使用示例 ===\n");
        
        // 示例1: 创建单个线程
        System.out.println("1. 创建单个线程:");
        ThreadFactory.createAndStart(() -> {
            System.out.println("单个线程执行: " + Thread.currentThread().getName());
            try { Thread.sleep(1000); } catch (InterruptedException e) {}
        }, "SingleThread");
        
        Thread.sleep(1500);
        
        // 示例2: 批量创建相同任务的线程
        System.out.println("\n2. 批量创建相同任务的线程:");
        Thread[] workers = ThreadFactory.createMultiple(() -> {
            String threadName = Thread.currentThread().getName();
            System.out.println("工作线程 " + threadName + " 开始执行");
            try { Thread.sleep(2000); } catch (InterruptedException e) {}
            System.out.println("工作线程 " + threadName + " 执行完成");
        }, 3, "Worker-");
        
        // 等待所有工作线程完成
        ThreadFactory.waitForAll(workers);
        
        // 示例3: 批量创建不同任务的线程
        System.out.println("\n3. 批量创建不同任务的线程:");
        Runnable[] tasks = {
            () -> {
                System.out.println("任务1: 计算数据");
                try { Thread.sleep(1000); } catch (InterruptedException e) {}
                System.out.println("任务1: 完成");
            },
            () -> {
                System.out.println("任务2: 处理文件");
                try { Thread.sleep(1500); } catch (InterruptedException e) {}
                System.out.println("任务2: 完成");
            },
            () -> {
                System.out.println("任务3: 网络请求");
                try { Thread.sleep(800); } catch (InterruptedException e) {}
                System.out.println("任务3: 完成");
            }
        };
        
        Thread[] taskThreads = ThreadFactory.createMultiple(tasks, "Task-");
        ThreadFactory.waitForAll(taskThreads);
        
        // 示例4: 使用线程池异步执行
        System.out.println("\n4. 使用线程池异步执行:");
        CompletableFuture<Void>[] futures = ThreadFactory.runMultipleAsync(new Runnable[]{
            () -> System.out.println("异步任务1: " + Thread.currentThread().getName()),
            () -> System.out.println("异步任务2: " + Thread.currentThread().getName()),
            () -> System.out.println("异步任务3: " + Thread.currentThread().getName())
        });
        
        // 等待所有异步任务完成
        CompletableFuture.allOf(futures).join();
        
        // 示例5: 创建守护线程
        System.out.println("\n5. 创建守护线程:");
        ThreadFactory.createDaemonThread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                System.out.println("守护线程运行中...");
                try { Thread.sleep(1000); } catch (InterruptedException e) {
                    break;
                }
            }
        }, "DaemonThread");
        
        Thread.sleep(3000);
        
        // 示例6: 可中断任务
        System.out.println("\n6. 可中断任务示例:");
        Thread interruptibleThread = ThreadFactory.createAndStart(
            ThreadFactory.interruptibleTask(() -> {
                try {
                    System.out.println("可中断任务开始执行");
                    Thread.sleep(5000); // 模拟长时间运行的任务
                    System.out.println("可中断任务正常完成");
                } catch (InterruptedException e) {
                    throw new RuntimeException(e); // 转换为RuntimeException以便被包装器捕获
                }
            }),
            "InterruptibleTask"
        );
        
        // 2秒后中断任务
        Thread.sleep(2000);
        interruptibleThread.interrupt();
        
        Thread.sleep(1000);
        
        System.out.println("\n=== 所有示例完成 ===");
        
        // 关闭线程池
        ThreadFactory.shutdown();
    }
}

