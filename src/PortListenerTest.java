import java.io.IOException;
import java.net.BindException;
import java.net.ServerSocket;

/**
 * 端口监听测试程序
 * 功能：尝试绑定并监听一个指定端口，以检查该端口是否可用。
 */
public class PortListenerTest {

    private static final int TEST_PORT = 48080;

    public static void main(String[] args) {
        // 使用 try-with-resources 语句可以确保 ServerSocket 在使用后被自动关闭
        try (ServerSocket serverSocket = new ServerSocket(TEST_PORT)) {
            // 如果执行到这里，说明 ServerSocket 成功创建，端口是可用的
            System.out.println("端口 " + TEST_PORT + " 可用！");
            System.out.println("测试程序将在此端口上短暂监听，然后自动关闭。");
            Thread.sleep(500000);
            // 为了让程序保持运行一小段时间，可以看到监听状态，可以加上一行等待代码
            // serverSocket.accept(); // 如果需要实际接受连接，则取消此行注释

        } catch (BindException e) {
            // 这是最关键的异常：当端口已经被占用时，会抛出此异常
            System.err.println("端口 " + TEST_PORT + " 已被占用！");
            System.err.println("详细信息: " + e.getMessage());
        } catch (IOException e) {
            // 其他可能发生的IO异常
            System.err.println("在尝试监听端口 " + TEST_PORT + " 时发生IO异常。");
            e.printStackTrace();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}