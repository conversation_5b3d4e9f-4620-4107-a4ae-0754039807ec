/**
 * 简单的线程创建工具类
 */
public class ThreadHelper {
    
    /**
     * 创建并启动一个新线程执行指定任务
     * @param task 要执行的任务
     * @param threadName 线程名称
     * @return 创建的线程对象
     */
    public static Thread createAndStart(Runnable task, String threadName) {
        Thread thread = new Thread(task, threadName);
        thread.start();
        return thread;
    }
    
    /**
     * 批量创建并启动多个线程执行不同任务
     * @param tasks 要执行的任务
     * @param namePrefix 线程名称前缀
     * @return 创建的线程数组
     */
    public static Thread[] createMultiple(Runnable[] tasks, String namePrefix) {
        Thread[] threads = new Thread[tasks.length];
        for (int i = 0; i < tasks.length; i++) {
            threads[i] = createAndStart(tasks[i], namePrefix + (i + 1));
        }
        return threads;
    }

    /**
     * 等待所有线程完成
     * @param threads 要等待的线程数组
     */
    public static void waitForAll(Thread[] threads) {
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 创建一个处理异常的任务包装器
     * @param task 原始任务
     * @return 包装后的任务
     */
    public static Runnable safeTask(RunnableWithException task) {
        return () -> {
            try {
                task.run();
            } catch (Exception e) {
                System.err.println("线程执行异常: " + e.getMessage());
            }
        };
    }
    
    /**
     * 可抛出异常的Runnable接口
     */
    @FunctionalInterface
    public interface RunnableWithException {
        void run() throws Exception;
    }


    // 示例用法
    public static void main(String[] args) {
        // 循环输出 1~10
       for (int i = 1; i <= 10; i++) {
            final int num = i;
            createAndStart(() -> {
                System.out.println("线程 " + Thread.currentThread().getName() + " 输出: " + num);
                try {
                    Thread.sleep(500); // 模拟工作
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }, "Thread-" + i);
        }
        
    }

}
