
public class DiamondGenerator {

    public static String diamond() {
        return diamond(5);
    }


    public static String diamond(int n) {
        return diamond(n, '*');
    }

    public static String diamond(int n, char... color) {
        StringBuilder diamond = new StringBuilder();
        char fillChar = color.length > 0? color[0] : '*';

        for (int i = 1; i <= n; i++) {
            for (int j = 1; j <= n - i; j++) {
                diamond.append(" ");
            }
            for (int k = 1; k <= 2 * i - 1; k++) {
                diamond.append(fillChar);
            }
            diamond.append("\n");
        }

        for (int i = n - 1; i >= 1; i--) {
            for (int j = 1; j <= n - i; j++) {
                diamond.append(" ");
            }
            for (int k = 1; k <= 2 * i - 1; k++) {
                diamond.append(fillChar);
            }
            diamond.append("\n");
        }

        return diamond.toString();
    }
}