import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class Main {

    static class Person {

        public Person(String name, String phoneNumber) {
            this.name = name;
            this.phoneNumber = phoneNumber;
        }

        private String name;
        private String phoneNumber;
        // getters and setters


        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPhoneNumber() {
            return phoneNumber;
        }

        public void setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
        }
    }



    public static void main(String[] args) {
        List<Person> bookList = new ArrayList<>();
        bookList.add(new Person("jack","18163138123"));
        bookList.add(new Person("martin",null));
        // 空指针异常
        Map<String, String> collect = bookList.stream()
                .collect(Collectors.toMap(
                        Person::getName,
                        Person::getPhoneNumber,
                        (v1, v2) -> v1 != null ? v1 : v2  // 明确处理 null 值
                ));

    }
}
