/**
 * 线程工具类使用示例
 */
public class ThreadHelperDemo {
    
    public static void main(String[] args) throws InterruptedException {
        System.out.println("=== 线程工具类使用示例 ===\n");
        
        // 示例1: 创建单个线程
        System.out.println("1. 创建单个线程:");
        Thread singleThread = ThreadHelper.createAndStart(() -> {
            System.out.println("单个线程执行: " + Thread.currentThread().getName());
            try { Thread.sleep(1000); } catch (InterruptedException e) {}
            System.out.println("单个线程完成: " + Thread.currentThread().getName());
        }, "SingleThread");
        
        singleThread.join(); // 等待单个线程完成
        
        // 示例2: 批量创建不同任务的线程
        System.out.println("\n2. 批量创建不同任务的线程:");
        Runnable[] tasks = {
            ThreadHelper.safeTask(() -> {
                System.out.println("任务1: 计算数据 - " + Thread.currentThread().getName());
                Thread.sleep(1000);
                System.out.println("任务1: 完成");
            }),
            ThreadHelper.safeTask(() -> {
                System.out.println("任务2: 处理文件 - " + Thread.currentThread().getName());
                Thread.sleep(1500);
                System.out.println("任务2: 完成");
            }),
            ThreadHelper.safeTask(() -> {
                System.out.println("任务3: 网络请求 - " + Thread.currentThread().getName());
                Thread.sleep(800);
                System.out.println("任务3: 完成");
            })
        };
        
        Thread[] taskThreads = ThreadHelper.createMultiple(tasks, "Task-");
        ThreadHelper.waitForAll(taskThreads);
        
        // 示例3: 异常处理示例
        System.out.println("\n3. 异常处理示例:");
        Thread[] errorThreads = ThreadHelper.createMultiple(new Runnable[]{
            ThreadHelper.safeTask(() -> {
                System.out.println("正常任务执行");
                Thread.sleep(500);
                System.out.println("正常任务完成");
            }),
            ThreadHelper.safeTask(() -> {
                System.out.println("异常任务开始");
                Thread.sleep(300);
                throw new RuntimeException("模拟异常");
            })
        }, "ErrorTest-");
        
        ThreadHelper.waitForAll(errorThreads);
        
        // 示例4: 生产者消费者模式
        System.out.println("\n4. 生产者消费者示例:");
        final Object lock = new Object();
        final int[] counter = {0};
        final int maxItems = 5;
        
        Thread[] producerConsumer = ThreadHelper.createMultiple(new Runnable[]{
            // 生产者
            ThreadHelper.safeTask(() -> {
                for (int i = 0; i < maxItems; i++) {
                    synchronized (lock) {
                        counter[0]++;
                        System.out.println("生产者生产了项目 " + counter[0]);
                        lock.notify(); // 通知消费者
                    }
                    Thread.sleep(500);
                }
            }),
            // 消费者
            ThreadHelper.safeTask(() -> {
                int consumed = 0;
                while (consumed < maxItems) {
                    synchronized (lock) {
                        while (counter[0] == consumed) {
                            lock.wait(); // 等待生产者
                        }
                        consumed++;
                        System.out.println("消费者消费了项目 " + consumed);
                    }
                    Thread.sleep(700);
                }
            })
        }, "ProducerConsumer-");
        
        ThreadHelper.waitForAll(producerConsumer);
        
        System.out.println("\n=== 所有示例完成 ===");
    }
}
