import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 线程工厂类，提供便捷的线程创建和管理方法
 */
public class ThreadFactory {

    private static final ExecutorService defaultExecutor = Executors.newCachedThreadPool();
    
    /**
     * 创建并启动一个新线程执行指定任务
     * @param task 要执行的任务
     * @param threadName 线程名称
     * @return 创建的线程对象
     */
    public static Thread createAndStart(Runnable task, String threadName) {
        Thread thread = new Thread(task, threadName);
        thread.start();
        return thread;
    }
    
    /**
     * 创建并启动一个新线程执行指定任务（自动生成线程名）
     * @param task 要执行的任务
     * @return 创建的线程对象
     */
    public static Thread createAndStart(Runnable task) {
        return createAndStart(task, "Worker-" + System.currentTimeMillis());
    }
    
    /**
     * 创建并启动一个守护线程
     * @param task 要执行的任务
     * @param threadName 线程名称
     * @return 创建的线程对象
     */
    public static Thread createDaemonThread(Runnable task, String threadName) {
        Thread thread = new Thread(task, threadName);
        thread.setDaemon(true);
        thread.start();
        return thread;
    }
    
    /**
     * 批量创建并启动多个线程执行相同任务
     * @param task 要执行的任务
     * @param count 线程数量
     * @param namePrefix 线程名称前缀
     * @return 创建的线程数组
     */
    public static Thread[] createMultiple(Runnable task, int count, String namePrefix) {
        Thread[] threads = new Thread[count];
        for (int i = 0; i < count; i++) {
            threads[i] = createAndStart(task, namePrefix + (i + 1));
        }
        return threads;
    }
    
    /**
     * 批量创建并启动多个线程执行不同任务
     * @param tasks 要执行的任务数组
     * @param namePrefix 线程名称前缀
     * @return 创建的线程数组
     */
    public static Thread[] createMultiple(Runnable[] tasks, String namePrefix) {
        Thread[] threads = new Thread[tasks.length];
        for (int i = 0; i < tasks.length; i++) {
            threads[i] = createAndStart(tasks[i], namePrefix + (i + 1));
        }
        return threads;
    }
    
    /**
     * 使用线程池异步执行任务
     * @param task 要执行的任务
     * @return CompletableFuture对象
     */
    public static CompletableFuture<Void> runAsync(Runnable task) {
        return CompletableFuture.runAsync(task, defaultExecutor);
    }
    
    /**
     * 使用线程池异步执行多个任务
     * @param tasks 要执行的任务数组
     * @return CompletableFuture数组
     */
    @SuppressWarnings("unchecked")
    public static CompletableFuture<Void>[] runMultipleAsync(Runnable[] tasks) {
        CompletableFuture<Void>[] futures = new CompletableFuture[tasks.length];
        for (int i = 0; i < tasks.length; i++) {
            futures[i] = runAsync(tasks[i]);
        }
        return futures;
    }
    
    /**
     * 等待所有线程完成
     * @param threads 要等待的线程数组
     * @param timeoutMs 超时时间（毫秒），0表示无限等待
     * @return 是否所有线程都在超时时间内完成
     */
    public static boolean waitForAll(Thread[] threads, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        for (Thread thread : threads) {
            try {
                if (timeoutMs > 0) {
                    long remainingTime = timeoutMs - (System.currentTimeMillis() - startTime);
                    if (remainingTime <= 0) {
                        return false;
                    }
                    thread.join(remainingTime);
                    if (thread.isAlive()) {
                        return false;
                    }
                } else {
                    thread.join();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        return true;
    }
    
    /**
     * 等待所有线程完成（无超时）
     * @param threads 要等待的线程数组
     */
    public static void waitForAll(Thread[] threads) {
        waitForAll(threads, 0);
    }
    
    /**
     * 创建一个可中断的任务包装器
     * @param task 原始任务
     * @return 包装后的任务
     */
    public static Runnable interruptibleTask(Runnable task) {
        return () -> {
            try {
                task.run();
            } catch (Exception e) {
                if (e instanceof InterruptedException ||
                    (e.getCause() instanceof InterruptedException)) {
                    System.out.println("任务被中断: " + Thread.currentThread().getName());
                } else {
                    throw new RuntimeException(e);
                }
            }
        };
    }
    
    /**
     * 关闭默认线程池
     */
    public static void shutdown() {
        defaultExecutor.shutdown();
        try {
            if (!defaultExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                defaultExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            defaultExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
