public class DiamondPattern {
    public static void main(String[] args) {
        System.out.print(diamond(6, '@', '.'));
    }

    public static String diamond(int n, char... color) {
        char fill = (color.length > 0) ? color[0] : '*';  // 默认填充字符
        char space = (color.length > 1) ? color[1] : ' ';  // 默认空白字符

        StringBuilder sb = new StringBuilder();

        // 打印上半部分
        for (int i = 0; i <= n; i++) {
            sb.append(generateLine(n, i, fill, space)).append("\n");
        }
        // 打印下半部分（对称部分）
        for (int i = n - 1; i >= 0; i--) {
            sb.append(generateLine(n, i, fill, space)).append("\n");
        }

        return sb.toString();
    }

    private static String generateLine(int n, int i, char fill, char space) {
        StringBuilder line = new StringBuilder();

        // 前置空格部分
        for (int j = 0; j < n - i; j++) {
            line.append(space).append(space);
        }
        // 填充菱形左侧
        for (int j = 0; j < i; j++) {
            line.append(fill).append(space);
        }
        // 填充菱形右侧
        line.append(fill);
        return line.toString();
    }
}
