import java.util.concurrent.locks.*;

public class ReadWriteLockDemo {
    private static final ReadWriteLock rwLock = new ReentrantReadWriteLock();
    private static final Lock readLock = rwLock.readLock();
    private static final Lock writeLock = rwLock.writeLock();

    private static int sharedData = 0;

    public static void main(String[] args) {
        // 启动3个读线程
        for (int i = 0; i < 3; i++) {
            new Thread(ReadWriteLockDemo::readData, "Reader-" + i).start();
        }

        // 稍后启动1个写线程（写时会阻塞其他读）
        new Thread(() -> {
            try {
                Thread.sleep(2000); // 等读线程开始执行后再写
                writeData();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "Writer").start();
    }

    public static void readData() {
        while (true) {
            readLock.lock();
            try {
                System.out.println(Thread.currentThread().getName() + " reading: " + sharedData);
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                readLock.unlock();
            }
        }
    }

    public static void writeData() {
        writeLock.lock();
        try {
            System.out.println(Thread.currentThread().getName() + " writing...");
            sharedData += 1;
            Thread.sleep(3000); // 模拟写操作耗时
            System.out.println(Thread.currentThread().getName() + " finished writing: " + sharedData);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            writeLock.unlock();
        }
    }
}
