package ink.lusy;

import ink.lusy.CGLIB.AliSmsService;
import ink.lusy.CGLIB.CglibProxyFactory;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
public class Main {
    public static void main(String[] args) {
        AliSmsService aliSmsService = (AliSmsService) CglibProxyFactory.getProxy(AliSmsService.class);
        aliSmsService.send("java");


        ByteBuffer buffer = ByteBuffer.allocate(1024);

        // 写入数据
        String msg = "Hello, DirectByteBuffer!";
        buffer.put(msg.getBytes(StandardCharsets.UTF_8));

        // 切换模式
        buffer.flip();

        // 读取数据
        byte[] bytes = new byte[buffer.remaining()];
        buffer.get(bytes);

        // 打印结果
        System.out.println(new String(bytes, StandardCharsets.UTF_8));


    }
}