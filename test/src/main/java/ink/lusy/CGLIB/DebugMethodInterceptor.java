package ink.lusy.CGLIB;

import net.sf.cglib.proxy.MethodInterceptor;
import net.sf.cglib.proxy.MethodProxy;

import java.lang.reflect.Method;

public class DebugMethodInterceptor implements MethodInterceptor {
    @Override
    public Object intercept(Object o, Method method, Object[] objects, MethodProxy methodProxy) throws Throwable {
        // 调用方法前，添加自己的操作
        System.out.println("before method" + method.getName());
        Object object = methodProxy.invokeSuper(o, objects);
        // 调用方法后，添加自己的操作
        System.out.println("after method" + method.getName());
        return object;
    }
}
